# requirements: Numpy<2 cn-clip

import torch
from cn_clip import clip
from PIL import Image
from qdrant_client import QdrantClient
from qdrant_client.models import PointStruct, Distance, VectorParams
from qdrant_client.http.exceptions import UnexpectedResponse

import configat

torch.set_grad_enabled(False)


CLIP_MODULE = configat.resolve("@env:CLIP_MODULE", default="ViT-L-14")
QDRANT_URL = configat.resolve("@env:QDRANT_URL")
QDRANT_COLLECTION = f"CLIP_{CLIP_MODULE}"

qdrant_client = QdrantClient(url=QDRANT_URL)


class Service:
    _device = "cuda" if torch.cuda.is_available() else "cpu"

    def setup(self):
        self._model, self._preprocess = clip.load_from_name(
            CLIP_MODULE, device=self._device, download_root=".cache/clip"
        )
        self._model.eval()

        try:
            qdrant_client.create_collection(
                collection_name=QDRANT_COLLECTION,
                vectors_config=VectorParams(
                    size=self._model.visual.output_dim, distance=Distance.COSINE
                ),
            )
        except UnexpectedResponse as e:
            if e.status_code != 409:
                raise e

    def add_image(self, id: int | str, image: Image.Image):
        image = self._preprocess(image).unsqueeze(0).to(self._device)  # type: ignore
        image_features = self._model.encode_image(image)
        image_features /= image_features.norm(dim=-1, keepdim=True)
        vector = image_features.squeeze(0).cpu().numpy()

        qdrant_client.upsert(
            collection_name=QDRANT_COLLECTION,
            points=[
                PointStruct(
                    id=id,
                    vector=vector.tolist(),
                )
            ],
        )

    def update_payload(self, id, payload: dict):
        qdrant_client.set_payload(
            collection_name=QDRANT_COLLECTION,
            points=[id],
            payload=payload,
        )

    def search_images(self, query: str, *, score_threshold: float | None = None):
        text = clip.tokenize(query).to(self._device)
        text_features = self._model.encode_text(text)
        text_features /= text_features.norm(dim=-1, keepdim=True)
        vector = text_features.squeeze(0).cpu().numpy()
        return qdrant_client.search(
            collection_name=QDRANT_COLLECTION,
            query_vector=vector.tolist(),
            score_threshold=score_threshold,
        )
