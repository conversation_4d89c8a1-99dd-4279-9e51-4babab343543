# requirements: Numpy<2 cn-clip

import torch
from cn_clip import clip
from PIL import Image
from qdrant_client import QdrantClient
from qdrant_client.models import PointStruct, Distance, VectorParams
from qdrant_client.http.exceptions import UnexpectedResponse
from typing import Optional, Protocol

import configat

torch.set_grad_enabled(False)


CLIP_MODULE = configat.resolve("@env:CLIP_MODULE", default="ViT-L-14")
QDRANT_URL = configat.resolve("@env:QDRANT_URL")
QDRANT_COLLECTION = f"CLIP_{CLIP_MODULE}"


class QdrantClientProtocol(Protocol):
    """Protocol for Qdrant client to enable dependency injection."""

    def create_collection(self, collection_name: str, vectors_config) -> None: ...
    def upsert(self, collection_name: str, points) -> None: ...
    def set_payload(self, collection_name: str, points, payload: dict) -> None: ...
    def search(
        self,
        collection_name: str,
        query_vector,
        score_threshold: Optional[float] = None,
    ): ...


class CLIPModelProtocol(Protocol):
    """Protocol for CLIP model to enable dependency injection."""

    def encode_image(self, image): ...
    def encode_text(self, text): ...
    @property
    def visual(self): ...


def create_default_qdrant_client() -> QdrantClient:
    """Factory function to create default Qdrant client."""
    return QdrantClient(url=QDRANT_URL)


def load_default_clip_model(device: str):
    """Factory function to load default CLIP model."""
    model, preprocess = clip.load_from_name(
        CLIP_MODULE, device=device, download_root=".cache/clip"
    )
    model.eval()
    return model, preprocess


class Service:
    _device = "cuda" if torch.cuda.is_available() else "cpu"

    def __init__(
        self,
        qdrant_client: Optional[QdrantClientProtocol] = None,
        clip_model: Optional[CLIPModelProtocol] = None,
        clip_preprocess=None,
        collection_name: Optional[str] = None,
    ):
        """Initialize Service with optional dependency injection for testing."""
        self._qdrant_client = qdrant_client or create_default_qdrant_client()
        self._model = clip_model
        self._preprocess = clip_preprocess
        self._collection_name = collection_name or QDRANT_COLLECTION
        self._is_setup = False

    def setup(self):
        """Setup the service by loading CLIP model and creating Qdrant collection."""
        if self._is_setup:
            return

        # Load CLIP model if not provided via dependency injection
        if self._model is None or self._preprocess is None:
            self._model, self._preprocess = load_default_clip_model(self._device)

        try:
            self._qdrant_client.create_collection(
                collection_name=self._collection_name,
                vectors_config=VectorParams(
                    size=self._model.visual.output_dim, distance=Distance.COSINE
                ),
            )
        except UnexpectedResponse as e:
            if e.status_code != 409:
                raise e

        self._is_setup = True

    def add_image(self, id: int | str, image: Image.Image):
        image = self._preprocess(image).unsqueeze(0).to(self._device)  # type: ignore
        image_features = self._model.encode_image(image)
        image_features /= image_features.norm(dim=-1, keepdim=True)
        vector = image_features.squeeze(0).cpu().numpy()

        qdrant_client.upsert(
            collection_name=QDRANT_COLLECTION,
            points=[
                PointStruct(
                    id=id,
                    vector=vector.tolist(),
                )
            ],
        )

    def update_payload(self, id, payload: dict):
        qdrant_client.set_payload(
            collection_name=QDRANT_COLLECTION,
            points=[id],
            payload=payload,
        )

    def search_images(self, query: str, *, score_threshold: float | None = None):
        text = clip.tokenize(query).to(self._device)
        text_features = self._model.encode_text(text)
        text_features /= text_features.norm(dim=-1, keepdim=True)
        vector = text_features.squeeze(0).cpu().numpy()
        return qdrant_client.search(
            collection_name=QDRANT_COLLECTION,
            query_vector=vector.tolist(),
            score_threshold=score_threshold,
        )
