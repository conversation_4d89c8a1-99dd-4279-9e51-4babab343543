from contextlib import asynccontextmanager
from hashlib import md5
from fastapi import FastAPI, UploadFile
from PIL import Image

from pydantic import BaseModel

from services import Service


service = Service()


@asynccontextmanager
async def lifespan(app: FastAPI):
    service.setup()
    yield


app = FastAPI(lifespan=lifespan)


@app.post("/images")
async def add_image(file: UploadFile):
    id = md5(await file.read()).hexdigest()
    await file.seek(0)
    image = Image.open(file.file)
    service.add_image(id, image)
    return {
        "id": id,
    }


@app.put("/images/{id}/payload")
async def update_image_payload(id: str, payload: dict):
    service.update_payload(id, payload)


class SearchImageResponseResultItem(BaseModel):
    score: float
    payload: dict


class SearchImageResponse(BaseModel):
    results: list[SearchImageResponseResultItem]


@app.get("/images/search", response_model=SearchImageResponse)
async def search_images(q: str, score_threshold: float | None = None):
    results = service.search_images(q, score_threshold=score_threshold)
    return {
        "results": results,
    }
